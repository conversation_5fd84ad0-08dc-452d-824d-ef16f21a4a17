import { Routes } from '@angular/router';
import { RegisterInfluencerComponent } from './components/register-influencer/register-influencer.component';
import { RegisterCompanyComponent } from './components/register-company/register-company.component';
import { LoginComponent } from './components/login/login.component';
import { VerifyEmailComponent } from './components/verify-email/verify-email.component';
import { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
import { ProfileComponent } from './components/profile/profile.component';
import { AuthCallbackComponent } from './components/auth-callback/auth-callback.component';
import { HomeComponent } from './components/home/<USER>';

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'register/influencer', component: RegisterInfluencerComponent },
  { path: 'register/company', component: RegisterCompanyComponent },
  { path: 'login', component: LoginComponent },
  { path: 'verify-email/:token', component: VerifyEmailComponent },
  { path: 'forgot-password', component: ForgotPasswordComponent },
  { path: 'reset-password', component: ResetPasswordComponent },
  { path: 'profile', component: ProfileComponent },
  { path: 'auth/:provider/callback', component: AuthCallbackComponent },
];